/**
 * 合并式词语输入组件
 * 🎯 核心价值：标题和内容合并的一体化词语输入框，支持展开/折叠和横向滚动
 * 📦 功能范围：词语集合标题显示、词语列表展示、输入框、展开折叠控制
 * 🔄 架构设计：一体化容器设计，与矩阵系统颜色同步
 */

'use client';

import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey,
  WordValidationResult
} from '@/core/matrix/MatrixTypes';
import { toast } from '@/core/ui/ToastStore';
import {
  getWordCollectionBackgroundColor,
  getWordCollectionDisplayName,
  getWordCollectionTextColor,
  parseInputText
} from '@/core/word/WordHook';
import { useWordInputStore, useWordStore } from '@/core/word/WordStore';
import React, { useCallback, useRef, useState } from 'react';

// ===== 组件属性 =====

interface CombinedWordInputProps {
  /** 词语集合标识 */
  collectionKey: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 是否折叠状态 */
  collapsed?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入变化回调 */
  onChange?: (words: string[]) => void;
  /** 验证结果回调 */
  onValidation?: (results: WordValidationResult[]) => void;
}

// ===== 词语标签组件 =====

interface WordTagProps {
  text: string;
  usageCount: number;
  backgroundColor: string;
  textColor: string;
  isSelected?: boolean;
  isDuplicate?: boolean;
  duplicateColor?: string;
  onRemove: () => void;
}

const WordTag: React.FC<WordTagProps> = ({
  text,
  usageCount,
  backgroundColor,
  textColor,
  isSelected = false,
  isDuplicate = false,
  duplicateColor,
  onRemove
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const getTagStyle = () => {
    if (isSelected) {
      return {
        backgroundColor: '#fbbf24',
        color: '#000',
        border: '2px solid #f59e0b',
        fontWeight: 'bold'
      };
    }

    if (isDuplicate && duplicateColor) {
      return {
        backgroundColor: duplicateColor,
        color: '#000',
        border: '1px solid rgba(0,0,0,0.2)'
      };
    }

    return {
      backgroundColor: backgroundColor + '80',
      color: textColor,
      border: '1px solid rgba(0,0,0,0.2)'
    };
  };

  return (
    <span
      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium mr-1 mb-1 relative cursor-pointer group"
      style={getTagStyle()}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {text}

      {/* 悬停时显示的删除按钮 */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onRemove();
        }}
        className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
        style={{ fontSize: '8px', lineHeight: '1' }}
        title="删除词语"
      >
        ×
      </button>

      {/* 工具提示 */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black text-white text-xs rounded whitespace-nowrap z-10">
          {isDuplicate ? `重复词语 | 使用${usageCount}次` : `使用${usageCount}次`}
        </div>
      )}
    </span>
  );
};

// ===== 主组件 =====

const CombinedWordInput: React.FC<CombinedWordInputProps> = ({
  collectionKey,
  color,
  level,
  collapsed = false,
  placeholder = '输入词语，用逗号分隔...',
  className = '',
  onChange,
  onValidation
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [isExpanded, setIsExpanded] = useState(!collapsed);

  // 获取词语集合状态
  const {
    getCollection,
    validateInput,
    addWord,
    removeWord,
    toggleCollectionCollapse,
    checkCrossCollectionDuplicate,
    getWordHighlightColor
  } = useWordStore();

  // 获取填词模式状态
  const { temporaryWord } = useWordInputStore();

  const collection = getCollection(collectionKey);
  const displayName = getWordCollectionDisplayName(color, level);
  const backgroundColor = getWordCollectionBackgroundColor(color);
  const textColor = getWordCollectionTextColor(color);

  // 显示错误提示
  const showErrorMessage = useCallback((message: string) => {
    toast.error(message, {
      duration: 3000,
      position: 'top-right'
    });
  }, []);

  // 处理输入确认（逗号或回车）
  const handleInputConfirm = useCallback(() => {
    if (!inputValue.trim()) return;

    const newWords = parseInputText(inputValue);
    const validWords: string[] = [];
    const errorWords: string[] = [];

    // 逐个验证和添加词语
    newWords.forEach(word => {
      const validation = validateInput(collectionKey, word);
      if (validation.isValid) {
        const result = addWord(collectionKey, word);
        if (result.isValid) {
          validWords.push(word);

          // 检查是否为跨词语集合重复词语，显示提醒
          if (result.isDuplicate && result.duplicateCollections && result.duplicateCollections.length > 1) {
            const otherCollections = result.duplicateCollections.filter(lib => lib !== collectionKey);
            if (otherCollections.length > 0) {
              toast.warning(`词语"${word}"在其他词语集合中也存在`, {
                duration: 3000,
                position: 'top-right'
              });
            }
          }
        }
      } else {
        errorWords.push(word);
        console.warn(`词语"${word}"验证失败:`, validation.errors);
      }
    });

    // 清空输入框
    setInputValue('');

    // 显示错误信息
    if (errorWords.length > 0) {
      showErrorMessage(`无效词语: ${errorWords.join(', ')}`);
    }

    // 触发回调
    if (validWords.length > 0) {
      onChange?.(validWords);
    }
  }, [inputValue, collectionKey, validateInput, addWord, onChange, showErrorMessage]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // 自动格式化逗号：将中文逗号转换为英文逗号，并确保逗号后有空格
    value = value.replace(/，/g, ', ').replace(/,\s*/g, ', ');

    setInputValue(value);

    // 实时检测逗号并自动确认输入
    if (value.endsWith(', ') && value.trim().length > 2) {
      // 延迟500ms确保用户看到逗号，然后自动确认
      setTimeout(() => {
        handleInputConfirm();
      }, 500);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // Enter键直接确认输入
      handleInputConfirm();
    } else if (e.key === ',' || e.key === '，') {
      // 逗号键不阻止默认行为，让输入变化事件处理实时识别
      // 这样可以实现更灵敏的识别
    }
  };

  // 删除词语
  const handleRemoveWord = (wordId: string) => {
    if (collection) {
      removeWord(collectionKey, wordId);
    }
  };

  // 切换展开/折叠
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    toggleCollectionCollapse(collectionKey);
  };

  if (!collection) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={`relative rounded-md border transition-all duration-200 ${className}`}
      style={{
        backgroundColor: backgroundColor + '20',
        borderColor: backgroundColor + '40',
        color: textColor
      }}
    >
      {/* 主容器 - 一体化布局 */}
      <div className="flex items-center flex-wrap gap-1 p-3">
        {/* 折叠/展开按钮 */}
        <button
          onClick={handleToggleExpanded}
          className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-xs hover:bg-black hover:bg-opacity-10 rounded"
          style={{ color: textColor }}
        >
          {isExpanded ? '−' : '+'}
        </button>

        {/* 标题部分 */}
        <span className="flex-shrink-0 font-medium">
          【{displayName}[{collection.words.length}词]：
        </span>

        {/* 词语列表部分 */}
        <div className={`flex items-center gap-1 ${isExpanded ? 'flex-wrap' : 'overflow-x-auto whitespace-nowrap'}`}>
          {isExpanded ? (
            // 展开模式：显示所有词语
            <>
              {collection.words.map((word, index) => {
                // 检查是否为矩阵系统选中的词语
                const isSelected = temporaryWord === word.text;

                // 检查是否为跨词语集合重复词语
                const duplicateCheck = checkCrossCollectionDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateCollections.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <WordTag
                    key={word.id}
                    text={word.text}
                    usageCount={word.usageCount}
                    backgroundColor={backgroundColor}
                    textColor={textColor}
                    isSelected={isSelected}
                    isDuplicate={isDuplicate}
                    duplicateColor={duplicateColor}
                    onRemove={() => handleRemoveWord(word.id)}
                  />
                );
              })}

              {/* 输入框 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={collection.words.length === 0 ? placeholder : '继续输入...'}
                className="bg-transparent border-none outline-none min-w-[120px] flex-grow"
                style={{ color: textColor, fontSize: '14px' }}
              />
            </>
          ) : (
            // 折叠模式：显示前3个词语，其余用省略号
            <>
              {collection.words.slice(0, 3).map((word, index) => {
                // 检查是否为矩阵系统选中的词语
                const isSelected = temporaryWord === word.text;

                // 检查是否为跨词语集合重复词语
                const duplicateCheck = checkCrossCollectionDuplicate(word.text);
                const isDuplicate = duplicateCheck.isDuplicate && duplicateCheck.duplicateCollections.length > 1;
                const duplicateColor = isDuplicate ? getWordHighlightColor(word.text) : undefined;

                return (
                  <WordTag
                    key={word.id}
                    text={word.text}
                    usageCount={word.usageCount}
                    backgroundColor={backgroundColor}
                    textColor={textColor}
                    isSelected={isSelected}
                    isDuplicate={isDuplicate}
                    duplicateColor={duplicateColor}
                    onRemove={() => handleRemoveWord(word.id)}
                  />
                );
              })}

              {/* 省略号 */}
              {collection.words.length > 3 && (
                <span className="text-xs opacity-70">
                  +{collection.words.length - 3}
                </span>
              )}

              {/* 折叠模式下的输入框 */}
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onBlur={handleInputConfirm}
                placeholder={collection.words.length === 0 ? placeholder : '继续输入...'}
                className="bg-transparent border-none outline-none min-w-[120px] flex-grow"
                style={{ color: textColor, fontSize: '14px' }}
              />
            </>
          )}
        </div>

        {/* 结束标记 */}
        <span className="flex-shrink-0">】</span>
      </div>
    </div>
  );
};

export default CombinedWordInput;
export type { CombinedWordInputProps };

// ===== 向后兼容性导出 =====

/** @deprecated 使用 CombinedWordInput 替代 */
export const CombinedWordLibraryInput = CombinedWordInput;

/** @deprecated 使用 CombinedWordInputProps 替代 */
export type CombinedWordLibraryInputProps = CombinedWordInputProps;
