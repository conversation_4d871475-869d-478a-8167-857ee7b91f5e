/**
 * 词语管理主组件
 * 🎯 核心价值：统一的词语管理界面，支持29个词语集合的可视化管理
 * 📦 功能范围：词语集合列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import Button from '@/components/ui/Button';
import CombinedWordInput from '@/components/ui/WordInput';
import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey
} from '@/core/matrix/MatrixTypes';
import {
  AVAILABLE_WORD_COLLECTIONS,
  getWordCollectionDisplayName
} from '@/core/word/WordHook';
import { useWordStore } from '@/core/word/WordStore';
import React, { memo, useCallback, useEffect, useRef } from 'react';

// ===== 组件属性 =====

interface WordManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否为颜色词语模式（可选，用于外部控制） */
  isColorWordMode?: boolean;
}

// ===== 词语集合项组件 =====

interface WordCollectionItemProps {
  color: BasicColorType;
  level: DataLevel;
  collectionKey: WordLibraryKey;
}

const WordCollectionItem: React.FC<WordCollectionItemProps> = ({ color, level, collectionKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const {
    getCollection
  } = useWordStore();

  const collection = getCollection(collectionKey);
  const displayName = getWordCollectionDisplayName(color, level);

  // 检查是否为活跃词语集合
  const { activeCollection } = useWordStore();
  const isActiveCollection = activeCollection === collectionKey;

  // 滚动到活跃词语集合
  useEffect(() => {
    if (isActiveCollection && itemRef.current) {
      itemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [isActiveCollection]);

  if (!collection) return null;

  return (
    <div ref={itemRef} className="mb-3" data-word-collection={collectionKey}>
      {/* 合并式词语集合输入组件 */}
      <CombinedWordInput
        collectionKey={collectionKey}
        color={color}
        level={level}
        collapsed={collection.collapsed}
        placeholder={`输入${displayName}词语...`}
        className={isActiveCollection ? 'word-collection-active' : ''}
      />
    </div>
  );
};

// ===== 主组件 =====

const WordManagerComponent: React.FC<WordManagerProps> = ({
  className = '',
  style,
  isColorWordMode = true // 默认为true，保持向后兼容
}) => {
  const { resetAllCollections, exportData, importData } = useWordStore();

  // 如果不是【颜色】【词语】模式，显示提示
  if (!isColorWordMode) {
    return (
      <div className={`word-manager ${className} flex flex-col h-full items-center justify-center`} style={style}>
        <div className="text-center text-gray-500">
          <p className="text-sm">词语管理功能仅在【颜色】【词语】模式下可用</p>
          <p className="text-xs mt-1">请切换到颜色模式 + 词语内容模式</p>
        </div>
      </div>
    );
  }

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `word-collections-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词语集合吗？此操作不可撤销。')) {
      resetAllCollections();
    }
  }, [resetAllCollections]);

  return (
    <div className={`word-manager ${className} flex flex-col h-full`} style={style}>
      {/* 标题和操作按钮 */}
      <div className="flex-shrink-0 mb-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-800">词语管理</h3>
          <div className="flex gap-2">
            <Button
              onClick={handleExport}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              导出
            </Button>
            <Button
              onClick={handleImport}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              导入
            </Button>
            <Button
              onClick={handleReset}
              variant="outline"
              size="sm"
              className="text-xs text-red-600 hover:text-red-700"
            >
              重置
            </Button>
          </div>
        </div>

        <div className="text-xs text-gray-500 mb-2">
          <p>支持29个颜色级别组合的词语集合</p>
          <p>每个词语2-4个字符，支持重复检测和高亮</p>
        </div>
      </div>

      {/* 词语集合列表 - 可滚动容器 */}
      <div className="flex-1 overflow-y-auto space-y-1 pr-2">
        {AVAILABLE_WORD_COLLECTIONS.map(({ color, level }) => {
          const collectionKey = `${color}-${level}` as WordLibraryKey;
          return (
            <WordCollectionItem
              key={collectionKey}
              color={color}
              level={level}
              collectionKey={collectionKey}
            />
          );
        })}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const WordManager = memo(WordManagerComponent);

WordManager.displayName = 'WordManager';

export default WordManager;
export type { WordManagerProps };

// ===== 向后兼容性导出 =====

/** @deprecated 使用 WordManager 替代 */
export const WordLibraryManager = WordManager;

/** @deprecated 使用 WordManagerProps 替代 */
export type WordLibraryManagerProps = WordManagerProps;
