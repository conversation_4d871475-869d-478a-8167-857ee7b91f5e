/**
 * 词语系统状态存储
 * 🎯 核心价值：统一的词语状态管理，支持持久化和实时同步
 * 📦 功能范围：词语状态、词语管理、填词模式、数据持久化
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持本地存储
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type { BasicColorType, DataLevel } from '../matrix/MatrixTypes';
import type {
  WordCollection,
  WordEntry,
  WordInputState,
  WordKey,
  WordState,
  WordValidationResult
} from './WordTypes';

import {
  addWordToGlobalIndex,
  assignDuplicateHighlightColor,
  buildGlobalWordIndex,
  checkWordDuplicateInCollection,
  createInitialWordState,
  createWordEntry,
  isCrossCollectionDuplicate,
  removeWordFromGlobalIndex,
  updateDuplicateWordsMap,
  validateWordText
} from './WordHook';

import { WORD_STORAGE_CONFIG } from './WordConfig';
import { createWordKey } from './WordTypes';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== Store 接口定义 =====

interface WordStore extends WordState {
  // ===== 词语集合管理 =====

  /** 添加词语到指定词语集合 */
  addWord: (collectionKey: WordKey, text: string) => WordValidationResult;

  /** 从词语集合中删除词语 */
  removeWord: (collectionKey: WordKey, wordId: string) => boolean;

  /** 更新词语文本 */
  updateWord: (collectionKey: WordKey, wordId: string, newText: string) => WordValidationResult;

  /** 批量添加词语 */
  addWords: (collectionKey: WordKey, texts: string[]) => WordValidationResult[];

  /** 清空词语集合 */
  clearCollection: (collectionKey: WordKey) => void;

  /** 切换词语集合折叠状态 */
  toggleCollectionCollapse: (collectionKey: WordKey) => void;

  /** 设置活跃词语集合 */
  setActiveCollection: (collectionKey: WordKey | null) => void;

  // ===== 词语查询 =====

  /** 获取词语集合 */
  getCollection: (collectionKey: WordKey) => WordCollection | undefined;

  /** 获取词语 */
  getWord: (collectionKey: WordKey, wordId: string) => WordEntry | undefined;

  /** 搜索词语 */
  searchWords: (query: string) => Array<{ word: WordEntry; collectionKey: WordKey }>;

  /** 获取匹配的词语集合（根据颜色和级别） */
  getMatchingCollection: (color: BasicColorType, level: DataLevel) => WordCollection | undefined;

  // ===== 验证和检测 =====

  /** 验证词语 */
  validateWordText: (collectionKey: WordKey, text: string) => WordValidationResult;

  /** 输入验证（阻止性）- 用于UI层验证 */
  validateInput: (collectionKey: WordKey, text: string) => { isValid: boolean; errors: string[] };

  /** 检测跨词语集合重复（提醒性） */
  checkCrossCollectionDuplicate: (text: string) => { isDuplicate: boolean; duplicateCollections: WordKey[] };

  /** 更新重复词语映射 */
  updateDuplicateMap: () => void;

  /** 获取重复词语信息 */
  getDuplicateInfo: (text: string) => WordKey[] | undefined;

  /** 获取词语高亮颜色 */
  getWordHighlightColor: (word: string) => string | undefined;

  /** 设置词语高亮颜色 */
  setWordHighlightColor: (word: string, color: string) => void;

  /** 清除词语高亮颜色 */
  clearWordHighlightColor: (word: string) => void;

  // ===== 数据管理 =====

  /** 导出词语数据 */
  exportData: () => string;

  /** 导入词语数据 */
  importData: (data: string) => boolean;

  /** 重置所有词语集合 */
  resetAllCollections: () => void;

  /** 获取统计信息 */
  getStatistics: () => {
    totalCollections: number;
    totalWords: number;
    duplicateWords: number;
    collectionStats: Array<{ collectionKey: WordKey; wordCount: number }>;
  };
}

// ===== 填词模式状态管理 =====

interface WordInputStore extends WordInputState {
  /** 激活填词模式 */
  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => Promise<void>;

  /** 退出填词模式 */
  deactivateWordInput: () => void;

  /** 选择下一个词语 */
  selectNextWord: () => void;

  /** 选择上一个词语 */
  selectPreviousWord: () => void;

  /** 确认选择当前词语 */
  confirmWordSelection: () => WordEntry | null;

  /** 删除当前单元格的词语 */
  clearCellWord: () => void;

  /** 检查词语集合是否为空并显示提醒 */
  checkCollectionEmpty: () => boolean;
}

// ===== 创建词语管理Store =====

export const useWordStore = create<WordStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...createInitialWordState(),

      // ===== 词语集合管理实现 =====

      addWord: (collectionKey: WordKey, text: string): WordValidationResult => {
        const trimmedText = text.trim();
        const state = get();
        const collection = state.collections.get(collectionKey);

        if (!collection) {
          return {
            isValid: false,
            errors: ['词语集合不存在'],
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 基础格式验证
        const formatValidation = validateWordText(trimmedText);
        if (!formatValidation.isValid) {
          return {
            isValid: false,
            errors: formatValidation.errors,
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 检查集合内重复
        const isDuplicateInCollection = checkWordDuplicateInCollection(collection, trimmedText);
        if (isDuplicateInCollection) {
          return {
            isValid: false,
            errors: ['该词语在当前词语集合中已存在'],
            isDuplicate: true,
            duplicateCollections: [collectionKey]
          };
        }

        // 检查跨集合重复（仅提醒，不阻止）
        const crossCollectionCheck = isCrossCollectionDuplicate(state.globalWordIndex, trimmedText);

        // 创建新词语
        const { color, level } = collection;
        const newWord = createWordEntry(trimmedText, color, level);

        set(produce((draft: WordState) => {
          const draftCollection = draft.collections.get(collectionKey);
          if (draftCollection) {
            draftCollection.words.push(newWord);
            draftCollection.lastUpdated = new Date();

            // 更新全局索引
            addWordToGlobalIndex(draft.globalWordIndex, trimmedText, collectionKey);

            // 如果跨集合重复，分配高亮颜色
            if (crossCollectionCheck.isDuplicate) {
              if (!draft.wordHighlightColors.has(trimmedText)) {
                const highlightColor = assignDuplicateHighlightColor(draft.usedHighlightColors);
                draft.wordHighlightColors.set(trimmedText, highlightColor);
                draft.usedHighlightColors.add(highlightColor);
              }
            }

            // 更新重复词语映射
            draft.duplicateWords = updateDuplicateWordsMap(draft.collections);
          }
        }));

        return {
          isValid: true,
          errors: [],
          isDuplicate: crossCollectionCheck.isDuplicate,
          duplicateCollections: crossCollectionCheck.collections
        };
      },

      removeWord: (collectionKey: WordKey, wordId: string): boolean => {
        const state = get();
        const collection = state.collections.get(collectionKey);

        if (!collection) return false;

        const wordIndex = collection.words.findIndex(word => word.id === wordId);
        if (wordIndex === -1) return false;

        const wordText = collection.words[wordIndex].text;

        set(produce((draft: WordState) => {
          const draftCollection = draft.collections.get(collectionKey);
          if (draftCollection) {
            draftCollection.words.splice(wordIndex, 1);
            draftCollection.lastUpdated = new Date();

            // 更新全局索引
            removeWordFromGlobalIndex(draft.globalWordIndex, wordText, collectionKey);

            // 检查是否还有其他集合包含此词语
            const remainingCollections = draft.globalWordIndex.get(wordText);
            if (!remainingCollections || remainingCollections.size <= 1) {
              // 清除高亮颜色
              const highlightColor = draft.wordHighlightColors.get(wordText);
              if (highlightColor) {
                draft.wordHighlightColors.delete(wordText);
                draft.usedHighlightColors.delete(highlightColor);
              }
            }

            // 更新重复词语映射
            draft.duplicateWords = updateDuplicateWordsMap(draft.collections);
          }
        }));

        return true;
      },

      updateWord: (collectionKey: WordKey, wordId: string, newText: string): WordValidationResult => {
        const trimmedText = newText.trim();
        const state = get();
        const collection = state.collections.get(collectionKey);

        if (!collection) {
          return {
            isValid: false,
            errors: ['词语集合不存在'],
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        const wordIndex = collection.words.findIndex(word => word.id === wordId);
        if (wordIndex === -1) {
          return {
            isValid: false,
            errors: ['词语不存在'],
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 基础格式验证
        const formatValidation = validateWordText(trimmedText);
        if (!formatValidation.isValid) {
          return {
            isValid: false,
            errors: formatValidation.errors,
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 检查集合内重复（排除当前词语）
        const isDuplicateInCollection = checkWordDuplicateInCollection(collection, trimmedText, wordId);
        if (isDuplicateInCollection) {
          return {
            isValid: false,
            errors: ['该词语在当前词语集合中已存在'],
            isDuplicate: true,
            duplicateCollections: [collectionKey]
          };
        }

        const oldText = collection.words[wordIndex].text;
        const crossCollectionCheck = isCrossCollectionDuplicate(state.globalWordIndex, trimmedText);

        set(produce((draft: WordState) => {
          const draftCollection = draft.collections.get(collectionKey);
          if (draftCollection && draftCollection.words[wordIndex]) {
            // 更新词语
            draftCollection.words[wordIndex].text = trimmedText;
            draftCollection.words[wordIndex].updatedAt = new Date();
            draftCollection.lastUpdated = new Date();

            // 更新全局索引
            removeWordFromGlobalIndex(draft.globalWordIndex, oldText, collectionKey);
            addWordToGlobalIndex(draft.globalWordIndex, trimmedText, collectionKey);

            // 处理高亮颜色
            if (crossCollectionCheck.isDuplicate) {
              if (!draft.wordHighlightColors.has(trimmedText)) {
                const highlightColor = assignDuplicateHighlightColor(draft.usedHighlightColors);
                draft.wordHighlightColors.set(trimmedText, highlightColor);
                draft.usedHighlightColors.add(highlightColor);
              }
            }

            // 清理旧词语的高亮颜色（如果不再重复）
            const oldWordCollections = draft.globalWordIndex.get(oldText);
            if (!oldWordCollections || oldWordCollections.size <= 1) {
              const oldHighlightColor = draft.wordHighlightColors.get(oldText);
              if (oldHighlightColor) {
                draft.wordHighlightColors.delete(oldText);
                draft.usedHighlightColors.delete(oldHighlightColor);
              }
            }

            // 更新重复词语映射
            draft.duplicateWords = updateDuplicateWordsMap(draft.collections);
          }
        }));

        return {
          isValid: true,
          errors: [],
          isDuplicate: crossCollectionCheck.isDuplicate,
          duplicateCollections: crossCollectionCheck.collections
        };
      },

      addWords: (collectionKey: WordKey, texts: string[]): WordValidationResult[] => {
        return texts.map(text => get().addWord(collectionKey, text));
      },

      clearCollection: (collectionKey: WordKey): void => {
        set(produce((draft: WordState) => {
          const collection = draft.collections.get(collectionKey);
          if (collection) {
            // 清理全局索引和高亮颜色
            collection.words.forEach(word => {
              removeWordFromGlobalIndex(draft.globalWordIndex, word.text, collectionKey);

              const remainingCollections = draft.globalWordIndex.get(word.text);
              if (!remainingCollections || remainingCollections.size === 0) {
                const highlightColor = draft.wordHighlightColors.get(word.text);
                if (highlightColor) {
                  draft.wordHighlightColors.delete(word.text);
                  draft.usedHighlightColors.delete(highlightColor);
                }
              }
            });

            collection.words = [];
            collection.lastUpdated = new Date();

            // 更新重复词语映射
            draft.duplicateWords = updateDuplicateWordsMap(draft.collections);
          }
        }));
      },

      toggleCollectionCollapse: (collectionKey: WordKey): void => {
        set(produce((draft: WordState) => {
          const collection = draft.collections.get(collectionKey);
          if (collection) {
            collection.collapsed = !collection.collapsed;
          }
        }));
      },

      setActiveCollection: (collectionKey: WordKey | null): void => {
        set(produce((draft: WordState) => {
          draft.activeCollection = collectionKey;
        }));
      },

      // ===== 词语查询实现 =====

      getCollection: (collectionKey: WordKey) => {
        return get().collections.get(collectionKey);
      },

      getWord: (collectionKey: WordKey, wordId: string) => {
        const collection = get().collections.get(collectionKey);
        return collection?.words.find(word => word.id === wordId);
      },

      searchWords: (query: string) => {
        const results: Array<{ word: WordEntry; collectionKey: WordKey }> = [];
        const state = get();

        state.collections.forEach((collection, collectionKey) => {
          collection.words.forEach(word => {
            if (word.text.includes(query)) {
              results.push({ word, collectionKey });
            }
          });
        });

        return results;
      },

      getMatchingCollection: (color: BasicColorType, level: DataLevel) => {
        const collectionKey = createWordKey(color, level);
        return get().collections.get(collectionKey);
      },

      // ===== 验证和检测实现 =====

      validateWordText: (collectionKey: WordKey, text: string): WordValidationResult => {
        const trimmedText = text.trim();
        const state = get();
        const collection = state.collections.get(collectionKey);

        if (!collection) {
          return {
            isValid: false,
            errors: ['词语集合不存在'],
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 基础格式验证
        const formatValidation = validateWordText(trimmedText);
        if (!formatValidation.isValid) {
          return {
            isValid: false,
            errors: formatValidation.errors,
            isDuplicate: false,
            duplicateCollections: []
          };
        }

        // 检查集合内重复
        const isDuplicateInCollection = checkWordDuplicateInCollection(collection, trimmedText);
        if (isDuplicateInCollection) {
          return {
            isValid: false,
            errors: ['该词语在当前词语集合中已存在'],
            isDuplicate: true,
            duplicateCollections: [collectionKey]
          };
        }

        // 检查跨集合重复
        const crossCollectionCheck = isCrossCollectionDuplicate(state.globalWordIndex, trimmedText);

        return {
          isValid: true,
          errors: crossCollectionCheck.isDuplicate ? ['该词语在其他词语集合中已存在'] : [],
          isDuplicate: crossCollectionCheck.isDuplicate,
          duplicateCollections: crossCollectionCheck.collections
        };
      },

      validateInput: (collectionKey: WordKey, text: string) => {
        const result = get().validateWordText(collectionKey, text);
        return {
          isValid: result.isValid,
          errors: result.errors
        };
      },

      checkCrossCollectionDuplicate: (text: string) => {
        const state = get();
        const result = isCrossCollectionDuplicate(state.globalWordIndex, text.trim());
        return {
          isDuplicate: result.isDuplicate,
          duplicateCollections: result.collections
        };
      },

      updateDuplicateMap: (): void => {
        set(produce((draft: WordState) => {
          draft.duplicateWords = updateDuplicateWordsMap(draft.collections);
        }));
      },

      getDuplicateInfo: (text: string) => {
        return get().duplicateWords.get(text);
      },

      getWordHighlightColor: (word: string) => {
        return get().wordHighlightColors.get(word);
      },

      setWordHighlightColor: (word: string, color: string): void => {
        set(produce((draft: WordState) => {
          draft.wordHighlightColors.set(word, color);
          draft.usedHighlightColors.add(color);
        }));
      },

      clearWordHighlightColor: (word: string): void => {
        set(produce((draft: WordState) => {
          const color = draft.wordHighlightColors.get(word);
          if (color) {
            draft.wordHighlightColors.delete(word);
            draft.usedHighlightColors.delete(color);
          }
        }));
      },

      // ===== 数据管理实现 =====

      exportData: (): string => {
        const state = get();
        const exportData = {
          collections: Array.from(state.collections.entries()),
          duplicateWords: Array.from(state.duplicateWords.entries()),
          wordHighlightColors: Array.from(state.wordHighlightColors.entries()),
          globalWordIndex: Array.from(state.globalWordIndex.entries()).map(([word, collections]) => [word, Array.from(collections)]),
          exportTime: new Date().toISOString(),
          version: WORD_STORAGE_CONFIG.STORAGE_VERSION
        };
        return JSON.stringify(exportData, null, 2);
      },

      importData: (data: string): boolean => {
        try {
          const importData = JSON.parse(data);

          if (!importData.collections || !Array.isArray(importData.collections)) {
            return false;
          }

          set(produce((draft: WordState) => {
            // 重建词语集合
            draft.collections = new Map(importData.collections);

            // 重建重复词语映射
            if (importData.duplicateWords) {
              draft.duplicateWords = new Map(importData.duplicateWords);
            }

            // 重建高亮颜色映射
            if (importData.wordHighlightColors) {
              draft.wordHighlightColors = new Map(importData.wordHighlightColors);
              draft.usedHighlightColors = new Set(Array.from(draft.wordHighlightColors.values()));
            }

            // 重建全局索引
            if (importData.globalWordIndex) {
              draft.globalWordIndex = new Map(
                importData.globalWordIndex.map(([word, collections]: [string, string[]]) => [word, new Set(collections)])
              );
            } else {
              // 如果没有全局索引，重新构建
              draft.globalWordIndex = buildGlobalWordIndex(draft.collections);
            }

            draft.lastSyncTime = new Date();
          }));

          return true;
        } catch (error) {
          console.error('导入词语数据失败:', error);
          return false;
        }
      },

      resetAllCollections: (): void => {
        set(createInitialWordState());
      },

      getStatistics: () => {
        const state = get();
        const collectionStats: Array<{ collectionKey: WordKey; wordCount: number }> = [];
        let totalWords = 0;

        state.collections.forEach((collection, collectionKey) => {
          const wordCount = collection.words.length;
          collectionStats.push({ collectionKey, wordCount });
          totalWords += wordCount;
        });

        return {
          totalCollections: state.collections.size,
          totalWords,
          duplicateWords: state.duplicateWords.size,
          collectionStats
        };
      }
    }),
    {
      name: WORD_STORAGE_CONFIG.STORAGE_KEY,
      version: WORD_STORAGE_CONFIG.STORAGE_VERSION,
      partialize: (state) => ({
        collections: Array.from(state.collections.entries()),
        activeCollection: state.activeCollection,
        duplicateWords: Array.from(state.duplicateWords.entries()),
        wordHighlightColors: Array.from(state.wordHighlightColors.entries()),
        usedHighlightColors: Array.from(state.usedHighlightColors),
        globalWordIndex: Array.from(state.globalWordIndex.entries()).map(([word, collections]) => [word, Array.from(collections)]),
        isLoading: state.isLoading,
        lastSyncTime: state.lastSyncTime
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 重新构建 Map 和 Set 数据结构
          state.collections = new Map(state.collections as any);
          state.duplicateWords = new Map(state.duplicateWords as any);
          state.wordHighlightColors = new Map(state.wordHighlightColors as any);
          state.usedHighlightColors = new Set(state.usedHighlightColors as any);
          state.globalWordIndex = new Map(
            (state.globalWordIndex as any).map(([word, collections]: [string, string[]]) => [word, new Set(collections)])
          );
        }
      }
    }
  )
);

// ===== 创建填词模式Store =====

export const useWordInputStore = create<WordInputStore>((set, get) => ({
  // 初始状态
  isActive: false,
  selectedCell: null,
  matchedCollection: null,
  selectedWordIndex: 0,
  availableWords: [],
  temporaryWord: null,
  isWordBound: false,

  activateWordInput: async (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => {
    const collectionKey = createWordKey(color, level);
    const wordStore = useWordStore.getState();
    const collection = wordStore.getCollection(collectionKey);

    if (!collection) {
      console.warn(`词语集合 ${collectionKey} 不存在`);
      return;
    }

    let selectedIndex = 0;
    if (boundWordId) {
      const boundIndex = collection.words.findIndex(word => word.id === boundWordId);
      if (boundIndex !== -1) {
        selectedIndex = boundIndex;
      }
    }

    set({
      isActive: true,
      selectedCell: { x, y },
      matchedCollection: collectionKey,
      selectedWordIndex: selectedIndex,
      availableWords: collection.words,
      temporaryWord: collection.words[selectedIndex]?.text || null,
      isWordBound: !!boundWordId
    });
  },

  deactivateWordInput: () => {
    set({
      isActive: false,
      selectedCell: null,
      matchedCollection: null,
      selectedWordIndex: 0,
      availableWords: [],
      temporaryWord: null,
      isWordBound: false
    });
  },

  selectNextWord: () => {
    const state = get();
    if (state.availableWords.length === 0) return;

    const nextIndex = (state.selectedWordIndex + 1) % state.availableWords.length;
    set({
      selectedWordIndex: nextIndex,
      temporaryWord: state.availableWords[nextIndex]?.text || null
    });
  },

  selectPreviousWord: () => {
    const state = get();
    if (state.availableWords.length === 0) return;

    const prevIndex = state.selectedWordIndex === 0
      ? state.availableWords.length - 1
      : state.selectedWordIndex - 1;
    set({
      selectedWordIndex: prevIndex,
      temporaryWord: state.availableWords[prevIndex]?.text || null
    });
  },

  confirmWordSelection: (): WordEntry | null => {
    const state = get();
    if (state.availableWords.length === 0) return null;

    const selectedWord = state.availableWords[state.selectedWordIndex];
    if (selectedWord) {
      set({ isWordBound: true });
    }
    return selectedWord || null;
  },

  clearCellWord: () => {
    set({
      temporaryWord: null,
      isWordBound: false
    });
  },

  checkCollectionEmpty: (): boolean => {
    const state = get();
    return state.availableWords.length === 0;
  }
}));

// ===== 向后兼容性导出 =====

/** @deprecated 使用 useWordStore 替代 */
export const useWordLibraryStore = useWordStore;

/** @deprecated 使用 WordStore 替代 */
export type WordLibraryStore = WordStore;
